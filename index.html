<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CURSOR AI - The Neo-Brutalist Code Editor</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto Mono', monospace;
            background-color: #F9F900;
            color: #000000;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Navigation */
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 40px;
            background-color: #F9F900;
            border-bottom: 3px solid #000000;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: #000000;
            text-decoration: none;
        }

        .nav-menu {
            display: flex;
            gap: 30px;
            list-style: none;
        }

        .nav-link {
            color: #000000;
            text-decoration: none;
            font-weight: 500;
            text-transform: uppercase;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            transform: translateY(-2px);
        }

        .download-btn {
            background-color: #FFFFFF;
            color: #000000;
            padding: 12px 24px;
            border: 3px solid #000000;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            text-transform: uppercase;
            box-shadow: 4px 4px 0px #000000;
            transition: all 0.2s ease;
        }

        .download-btn:hover {
            transform: translate(-2px, -2px);
            box-shadow: 6px 6px 0px #000000;
        }

        /* Hero Section */
        .hero {
            padding: 80px 40px;
            text-align: center;
            background-color: #F9F900;
            position: relative;
        }

        .hero-title {
            font-size: 72px;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: 24px;
            font-weight: 400;
            margin-bottom: 40px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-cta {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        .primary-btn {
            background-color: #FF00FF;
            color: #000000;
            padding: 20px 40px;
            border: 3px solid #000000;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 700;
            text-transform: uppercase;
            font-size: 18px;
            box-shadow: 6px 6px 0px #000000;
            transition: all 0.2s ease;
        }

        .primary-btn:hover {
            transform: translate(-3px, -3px);
            box-shadow: 9px 9px 0px #000000;
        }

        .secondary-btn {
            background-color: #FFFFFF;
            color: #000000;
            padding: 20px 40px;
            border: 3px solid #000000;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 18px;
            box-shadow: 6px 6px 0px #000000;
            transition: all 0.2s ease;
        }

        .secondary-btn:hover {
            transform: translate(-3px, -3px);
            box-shadow: 9px 9px 0px #000000;
        }

        /* Decorative Graphics */
        .hero-graphics {
            position: absolute;
            top: 50%;
            left: 50px;
            transform: translateY(-50%);
            width: 100px;
            height: 100px;
            border: 3px solid #000000;
            background-color: #43C4B2;
            border-radius: 15px;
        }

        .hero-graphics::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            width: 20px;
            height: 20px;
            background-color: #FF00FF;
            border: 2px solid #000000;
            border-radius: 50%;
        }

        .hero-graphics::after {
            content: '';
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 30px;
            height: 30px;
            background-color: #FFFFFF;
            border: 2px solid #000000;
        }

        .hero-graphics-right {
            position: absolute;
            top: 30%;
            right: 50px;
            transform: translateY(-50%);
            width: 80px;
            height: 80px;
            border: 3px solid #000000;
            background-color: #FFFFFF;
            transform: rotate(45deg);
        }

        /* Features Section */
        .features {
            background-color: #43C4B2;
            padding: 80px 40px;
            border-top: 3px solid #000000;
            border-bottom: 3px solid #000000;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .feature-card {
            background-color: #FFFFFF;
            border: 3px solid #000000;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 8px 8px 0px #000000;
            transition: all 0.2s ease;
        }

        .feature-card:hover {
            transform: translate(-4px, -4px);
            box-shadow: 12px 12px 0px #000000;
        }

        .feature-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            text-transform: uppercase;
        }

        .feature-description {
            font-size: 16px;
            font-weight: 400;
            line-height: 1.6;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background-color: #FF00FF;
            border: 3px solid #000000;
            border-radius: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 700;
        }

        /* Testimonials */
        .testimonials {
            background-color: #F9F900;
            padding: 80px 40px;
            text-align: center;
        }

        .testimonials-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 60px;
            text-transform: uppercase;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .testimonial-card {
            background-color: #43C4B2;
            border: 3px solid #000000;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 6px 6px 0px #000000;
            transition: all 0.2s ease;
        }

        .testimonial-card:hover {
            transform: translate(-3px, -3px);
            box-shadow: 9px 9px 0px #000000;
        }

        .testimonial-text {
            font-size: 16px;
            font-weight: 400;
            margin-bottom: 20px;
            font-style: italic;
        }

        .testimonial-author {
            font-size: 14px;
            font-weight: 700;
            text-transform: uppercase;
        }

        .testimonial-company {
            font-size: 12px;
            font-weight: 400;
            color: #000000;
            opacity: 0.7;
        }

        /* CTA Section */
        .cta {
            background-color: #FF00FF;
            padding: 80px 40px;
            text-align: center;
            border-top: 3px solid #000000;
            position: relative;
            overflow: hidden;
        }

        .cta-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 20px;
            text-transform: uppercase;
        }

        .cta-subtitle {
            font-size: 20px;
            font-weight: 400;
            margin-bottom: 40px;
        }

        /* Additional Decorative Elements */
        .features::before {
            content: '';
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background-color: #FF00FF;
            border: 3px solid #000000;
            border-radius: 50%;
        }

        .features {
            position: relative;
        }

        .cta::before {
            content: '';
            position: absolute;
            top: 30px;
            left: 30px;
            width: 60px;
            height: 60px;
            background-color: #43C4B2;
            border: 3px solid #000000;
            transform: rotate(45deg);
        }

        .cta::after {
            content: '';
            position: absolute;
            bottom: 30px;
            right: 30px;
            width: 80px;
            height: 80px;
            background-color: #FFFFFF;
            border: 3px solid #000000;
            border-radius: 20px;
        }

        /* Pixel Grid Pattern */
        .testimonials::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 20px 20px, #000000 2px, transparent 2px);
            background-size: 40px 40px;
            opacity: 0.1;
            pointer-events: none;
        }

        .testimonials {
            position: relative;
        }

        /* Window-like container for main content */
        .main-window {
            max-width: 1400px;
            margin: 0 auto;
            background-color: #F9F900;
            border: 3px solid #000000;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 12px 12px 0px #000000;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .window-header {
            background-color: #FFFFFF;
            border-bottom: 3px solid #000000;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .window-controls {
            display: flex;
            gap: 8px;
        }

        .window-control {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid #000000;
        }

        .window-control.close {
            background-color: #FF00FF;
        }

        .window-control.minimize {
            background-color: #F9F900;
        }

        .window-control.maximize {
            background-color: #43C4B2;
        }

        .window-title {
            font-weight: 700;
            font-size: 14px;
            text-transform: uppercase;
            margin-left: 10px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav {
                flex-direction: column;
                gap: 20px;
                padding: 20px;
            }

            .nav-menu {
                gap: 20px;
            }

            .hero-title {
                font-size: 48px;
            }

            .hero-subtitle {
                font-size: 18px;
            }

            .hero-cta {
                flex-direction: column;
            }

            .hero-graphics,
            .hero-graphics-right {
                display: none;
            }

            .features-grid,
            .testimonials-grid {
                grid-template-columns: 1fr;
            }

            .testimonials-title,
            .cta-title {
                font-size: 36px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="nav">
        <a href="#" class="logo">CURSOR</a>
        <ul class="nav-menu">
            <li><a href="#" class="nav-link">Features</a></li>
            <li><a href="#" class="nav-link">Pricing</a></li>
            <li><a href="#" class="nav-link">Enterprise</a></li>
            <li><a href="#" class="nav-link">Blog</a></li>
        </ul>
        <a href="#" class="download-btn">Download</a>
    </nav>

    <!-- Main Window Container -->
    <div class="main-window">
        <div class="window-header">
            <div class="window-controls">
                <div class="window-control close"></div>
                <div class="window-control minimize"></div>
                <div class="window-control maximize"></div>
            </div>
            <div class="window-title">Cursor AI - Code Editor</div>
        </div>

        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-graphics"></div>
            <div class="hero-graphics-right"></div>

            <h1 class="hero-title">THE AI<br>CODE EDITOR</h1>
            <p class="hero-subtitle">Built to make you extraordinarily productive, Cursor is the best way to code with AI.</p>

            <div class="hero-cta">
                <a href="#" class="primary-btn">Download for MacOS</a>
                <a href="#" class="secondary-btn">All Downloads</a>
            </div>
        </section>

    <!-- Features Section -->
    <section class="features">
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">Tab, Tab, Tab</h3>
                <p class="feature-description">Cursor lets you breeze through changes by predicting your next edit. Experience the magic of AI-powered autocomplete.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🧠</div>
                <h3 class="feature-title">Knows Your Codebase</h3>
                <p class="feature-description">Get answers from your codebase or refer to files or docs. Use the model's code in one click.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">💬</div>
                <h3 class="feature-title">Edit in Natural Language</h3>
                <p class="feature-description">Cursor lets you write code using instructions. Update entire classes or functions with a simple prompt.</p>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials">
        <h2 class="testimonials-title">Loved by World-Class Devs</h2>
        <div class="testimonials-grid">
            <div class="testimonial-card">
                <p class="testimonial-text">"Cursor is at least a 2x improvement over Copilot. It's amazing having an AI pair programmer."</p>
                <div class="testimonial-author">Ben Bernard</div>
                <div class="testimonial-company">Instacart</div>
            </div>

            <div class="testimonial-card">
                <p class="testimonial-text">"The tab completion is occasionally so magic it defies reality - about 25% of the time it anticipates exactly what I want to do."</p>
                <div class="testimonial-author">Kevin Whinnery</div>
                <div class="testimonial-company">OpenAI</div>
            </div>

            <div class="testimonial-card">
                <p class="testimonial-text">"Cursor is hands down my biggest workflow improvement in years. I can't imagine coding without it."</p>
                <div class="testimonial-author">Sawyer Hood</div>
                <div class="testimonial-company">Figma</div>
            </div>

            <div class="testimonial-card">
                <p class="testimonial-text">"I love writing code and Cursor is a necessity. Cursor is steps ahead of my brain, proposing multi-line edits."</p>
                <div class="testimonial-author">Andrew Milich</div>
                <div class="testimonial-company">Notion</div>
            </div>

            <div class="testimonial-card">
                <p class="testimonial-text">"Cursor is so good, and literally gets better/more feature-rich every couple of weeks."</p>
                <div class="testimonial-author">Morgan McGuire</div>
                <div class="testimonial-company">Weights & Biases</div>
            </div>

            <div class="testimonial-card">
                <p class="testimonial-text">"Someone finally put GPT into a code editor in a seamless way. It's so elegant and easy. No more copying and pasting."</p>
                <div class="testimonial-author">Andrew McCalip</div>
                <div class="testimonial-company">Varda</div>
            </div>
        </div>
    </section>

        <!-- CTA Section -->
        <section class="cta">
            <h2 class="cta-title">Try Cursor Now</h2>
            <p class="cta-subtitle">Download for free and experience the future of coding</p>
            <a href="#" class="primary-btn">Download for Free</a>
        </section>
    </div>

    <!-- Additional Decorative Elements Outside Window -->
    <div style="position: fixed; top: 20%; left: 20px; width: 60px; height: 60px; background-color: #43C4B2; border: 3px solid #000000; border-radius: 15px; z-index: -1;"></div>
    <div style="position: fixed; top: 60%; right: 30px; width: 40px; height: 40px; background-color: #FF00FF; border: 3px solid #000000; transform: rotate(45deg); z-index: -1;"></div>
    <div style="position: fixed; bottom: 20%; left: 50px; width: 80px; height: 20px; background-color: #FFFFFF; border: 3px solid #000000; z-index: -1;"></div>
</body>
</html>
